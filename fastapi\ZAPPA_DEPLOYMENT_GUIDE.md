# Zappa Deployment Guide for ReplyPal

This guide explains how to use Zappa for easy AWS Lambda deployment and management of your ReplyPal FastAPI application.

## What is Zappa?

Zappa is a tool that makes it super easy to deploy Python web applications (like FastAPI) to AWS Lambda and API Gateway. It handles all the complexity of packaging, uploading, and configuring AWS services automatically.

## Benefits of Using Zappa

- **One-command deployment**: Deploy with a single command
- **Automatic API Gateway setup**: No manual API Gateway configuration needed
- **Easy updates**: Update your code with one command
- **Built-in logging**: Easy access to CloudWatch logs
- **Rollback support**: Easily rollback to previous versions
- **Multiple environments**: Support for dev, staging, and production
- **Cost-effective**: Only pay for what you use with Lambda

## Prerequisites

1. **AWS CLI configured** with your `sumathy_aws` profile
2. **Python environment** with all dependencies installed
3. **S3 bucket** for Zappa deployments (will be created automatically)

## Quick Start

### 1. Install Dependencies

```bash
cd fastapi
pip install -r requirements.txt
```

### 2. First Deployment

```bash
# Deploy to production
python deploy.py deploy

# Or deploy to staging
python deploy.py deploy --stage staging
```

### 3. Update Existing Deployment

```bash
# Update production
python deploy.py update

# Update staging
python deploy.py update --stage staging
```

## Available Commands

### Deployment Commands

```bash
# Initial deployment
python deploy.py deploy [--stage production|staging|development]

# Update existing deployment
python deploy.py update [--stage production|staging|development]

# Remove deployment
python deploy.py undeploy [--stage production|staging|development]
```

### Management Commands

```bash
# Check deployment status
python deploy.py status [--stage production|staging|development]

# View recent logs
python deploy.py logs [--stage production|staging|development]

# Tail logs in real-time
python deploy.py tail [--stage production|staging|development]

# Rollback to previous version
python deploy.py rollback [--stage production|staging|development]
```

## Configuration

The deployment is configured in `zappa_settings.json`:

### Environment Variables

All your environment variables are automatically set during deployment:
- API keys (OpenAI, DeepSeek, Stripe)
- Database settings (DynamoDB tables)
- Application settings
- JWT secrets

### Stages

Three deployment stages are configured:

1. **Production** (`production`): Live environment
2. **Staging** (`staging`): Testing environment
3. **Development** (`development`): Development environment with mock DynamoDB

## Typical Workflow

### Initial Setup
```bash
# 1. Deploy to staging first
python deploy.py deploy --stage staging

# 2. Test the staging deployment
python deploy.py status --stage staging

# 3. Deploy to production when ready
python deploy.py deploy --stage production
```

### Regular Updates
```bash
# 1. Update staging
python deploy.py update --stage staging

# 2. Test changes
# ... test your changes ...

# 3. Update production
python deploy.py update --stage production
```

### Monitoring and Debugging
```bash
# Check status
python deploy.py status

# View logs
python deploy.py logs

# Monitor real-time logs
python deploy.py tail
```

### Emergency Rollback
```bash
# Rollback to previous version
python deploy.py rollback
```

## Getting Your API URL

After deployment, Zappa will show you the API Gateway URL. It will look like:
```
https://abc123def4.execute-api.us-east-1.amazonaws.com/production
```

Update your Chrome extension settings to use this URL.

## Environment-Specific Settings

### Production
- Full DynamoDB integration
- Production API keys
- Optimized for performance

### Staging
- Same as production but separate environment
- Good for testing before production deployment

### Development
- Mock DynamoDB for testing
- Debug logging enabled
- Safe for experimentation

## Troubleshooting

### Common Issues

1. **AWS Profile Not Found**
   ```bash
   aws configure --profile sumathy_aws
   ```

2. **S3 Bucket Permission Issues**
   - Ensure your AWS user has S3 permissions
   - The script will try to create the bucket automatically

3. **Lambda Timeout**
   - Increase timeout in `zappa_settings.json`
   - Current setting: 30 seconds

4. **Package Too Large**
   - Zappa automatically optimizes the package
   - Excludes unnecessary files (see `exclude` in config)

### Viewing Detailed Logs

```bash
# View recent logs
python deploy.py logs

# View logs from specific time
zappa logs production --since 2h

# Tail logs in real-time
python deploy.py tail
```

### Manual Zappa Commands

You can also use Zappa directly:

```bash
# Deploy
zappa deploy production

# Update
zappa update production

# Status
zappa status production

# Logs
zappa logs production

# Undeploy
zappa undeploy production
```

## Security Notes

1. **API Keys**: Stored as environment variables in Lambda
2. **AWS Permissions**: Uses your configured AWS profile
3. **HTTPS**: All traffic is automatically HTTPS via API Gateway
4. **CORS**: Configured to allow your Chrome extension

## Cost Optimization

- **Lambda**: Pay only for execution time
- **API Gateway**: Pay per request
- **S3**: Minimal cost for deployment packages
- **CloudWatch**: Logs are included in AWS free tier

Typical monthly cost for moderate usage: $1-5

## Comparison with Manual Deployment

| Feature | Manual Deployment | Zappa |
|---------|------------------|-------|
| Initial Setup | Complex (multiple steps) | Simple (one command) |
| Updates | Manual ZIP upload | One command |
| API Gateway | Manual configuration | Automatic |
| Rollback | Manual process | One command |
| Logs | AWS Console only | Command line access |
| Multiple Environments | Manual setup each | Built-in support |

## Next Steps

1. **Deploy to staging first**: `python deploy.py deploy --stage staging`
2. **Test your API**: Use the provided URL to test endpoints
3. **Update Chrome extension**: Point it to your new API URL
4. **Deploy to production**: `python deploy.py deploy --stage production`
5. **Set up monitoring**: Use `python deploy.py tail` to monitor logs

## Support

If you encounter issues:
1. Check the logs: `python deploy.py logs`
2. Verify AWS credentials: `aws sts get-caller-identity --profile sumathy_aws`
3. Check Zappa documentation: https://github.com/zappa/Zappa
