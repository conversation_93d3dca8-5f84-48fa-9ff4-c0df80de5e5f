#!/usr/bin/env python3
"""
Zappa deployment script for ReplyPal API
Provides easy commands for deploying and managing the FastAPI application on AWS Lambda
"""

import os
import sys
import subprocess
import json
import argparse
from pathlib import Path

def run_command(command, check=True):
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e

def check_aws_profile():
    """Check if the AWS profile is configured."""
    try:
        result = subprocess.run(
            "aws sts get-caller-identity --profile sumathy_aws", 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        print("✅ AWS profile 'sumathy_aws' is configured")
        return True
    except subprocess.CalledProcessError:
        print("❌ AWS profile 'sumathy_aws' is not configured or not working")
        print("Please configure your AWS credentials with: aws configure --profile sumathy_aws")
        return False

def check_s3_bucket():
    """Check if the S3 bucket exists, create if it doesn't."""
    bucket_name = "replypal-zappa-deployments"
    try:
        result = subprocess.run(
            f"aws s3 ls s3://{bucket_name} --profile sumathy_aws", 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        print(f"✅ S3 bucket '{bucket_name}' exists")
        return True
    except subprocess.CalledProcessError:
        print(f"📦 Creating S3 bucket '{bucket_name}'...")
        try:
            subprocess.run(
                f"aws s3 mb s3://{bucket_name} --profile sumathy_aws", 
                shell=True, 
                check=True
            )
            print(f"✅ Created S3 bucket '{bucket_name}'")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Failed to create S3 bucket '{bucket_name}'")
            return False

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    run_command("pip install -r requirements.txt")

def deploy(stage="production", update=False):
    """Deploy the application using Zappa."""
    print(f"🚀 {'Updating' if update else 'Deploying'} ReplyPal API to {stage}...")
    
    # Check prerequisites
    if not check_aws_profile():
        return False
    
    if not check_s3_bucket():
        return False
    
    # Install dependencies
    install_dependencies()
    
    # Deploy or update
    command = f"zappa {'update' if update else 'deploy'} {stage}"
    result = run_command(command, check=False)
    
    if result.returncode == 0:
        print(f"✅ Successfully {'updated' if update else 'deployed'} to {stage}")
        
        # Get the API URL
        try:
            status_result = subprocess.run(
                f"zappa status {stage}", 
                shell=True, 
                check=True, 
                capture_output=True, 
                text=True
            )
            print("\n" + "="*60)
            print("DEPLOYMENT SUCCESSFUL!")
            print("="*60)
            print(status_result.stdout)
            print("="*60)
        except subprocess.CalledProcessError:
            print("✅ Deployment successful, but couldn't get status")
        
        return True
    else:
        print(f"❌ Failed to {'update' if update else 'deploy'} to {stage}")
        return False

def undeploy(stage="production"):
    """Undeploy the application."""
    print(f"🗑️  Undeploying ReplyPal API from {stage}...")
    
    if not check_aws_profile():
        return False
    
    command = f"zappa undeploy {stage} --remove-logs"
    result = run_command(command, check=False)
    
    if result.returncode == 0:
        print(f"✅ Successfully undeployed from {stage}")
        return True
    else:
        print(f"❌ Failed to undeploy from {stage}")
        return False

def status(stage="production"):
    """Get the status of the deployment."""
    print(f"📊 Getting status for {stage}...")
    
    if not check_aws_profile():
        return False
    
    command = f"zappa status {stage}"
    run_command(command)

def logs(stage="production", tail=False):
    """Get logs from the deployment."""
    print(f"📋 Getting logs for {stage}...")
    
    if not check_aws_profile():
        return False
    
    command = f"zappa logs {stage}"
    if tail:
        command += " --since 1h"
    
    run_command(command)

def rollback(stage="production"):
    """Rollback to the previous deployment."""
    print(f"⏪ Rolling back {stage}...")
    
    if not check_aws_profile():
        return False
    
    command = f"zappa rollback {stage}"
    result = run_command(command, check=False)
    
    if result.returncode == 0:
        print(f"✅ Successfully rolled back {stage}")
        return True
    else:
        print(f"❌ Failed to rollback {stage}")
        return False

def main():
    parser = argparse.ArgumentParser(description="ReplyPal Zappa Deployment Tool")
    parser.add_argument("action", choices=[
        "deploy", "update", "undeploy", "status", "logs", "rollback", "tail"
    ], help="Action to perform")
    parser.add_argument("--stage", default="production", 
                       choices=["production", "staging", "development"],
                       help="Deployment stage (default: production)")
    
    args = parser.parse_args()
    
    # Change to the fastapi directory if not already there
    if not Path("zappa_settings.json").exists():
        print("❌ zappa_settings.json not found. Make sure you're in the fastapi directory.")
        sys.exit(1)
    
    if args.action == "deploy":
        success = deploy(args.stage, update=False)
    elif args.action == "update":
        success = deploy(args.stage, update=True)
    elif args.action == "undeploy":
        success = undeploy(args.stage)
    elif args.action == "status":
        success = status(args.stage)
    elif args.action == "logs":
        success = logs(args.stage, tail=False)
    elif args.action == "tail":
        success = logs(args.stage, tail=True)
    elif args.action == "rollback":
        success = rollback(args.stage)
    
    if not success and args.action in ["deploy", "update", "undeploy", "rollback"]:
        sys.exit(1)

if __name__ == "__main__":
    main()
