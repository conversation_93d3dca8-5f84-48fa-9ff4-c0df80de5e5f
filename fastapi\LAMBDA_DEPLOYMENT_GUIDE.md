# AWS Lambda Deployment Guide for ReplyPal

This guide explains how to deploy the ReplyPal FastAPI application to AWS Lambda using the generated `replypal_lambda.zip` package.

## Prerequisites

1. AWS CLI configured with appropriate permissions
2. AWS Lambda service access
3. API Gateway service access (optional, for HTTP endpoints)
4. DynamoDB tables already created (replypal-user-subscriptions, replypal-usage-records)

## Deployment Steps

### 1. Create Lambda Function

#### Using AWS Console:

1. Go to AWS Lambda Console
2. Click "Create function"
3. Choose "Author from scratch"
4. Function name: `replypal-api`
5. Runtime: `Python 3.9` or `Python 3.10`
6. Architecture: `x86_64`
7. Click "Create function"

#### Using AWS CLI:

```bash
aws lambda create-function \
  --function-name replypal-api \
  --runtime python3.9 \
  --role arn:aws:iam::YOUR_ACCOUNT:role/lambda-execution-role \
  --handler lambda_handler.handler \
  --zip-file fileb://replypal_lambda.zip \
  --timeout 30 \
  --memory-size 512
```

### 2. Upload Deployment Package

#### Using AWS Console:

1. In the Lambda function page, go to "Code" tab
2. Click "Upload from" → ".zip file"
3. Select `replypal_lambda.zip`
4. Click "Save"

#### Using AWS CLI:

```bash
aws lambda update-function-code \
  --function-name replypal-api \
  --zip-file fileb://replypal_lambda.zip
```

### 3. Configure Lambda Function

#### Handler Configuration:

- Handler: `lambda_handler.handler`

#### Environment Variables:

Set the following environment variables in Lambda:

```
# Application settings
APP_NAME=ReplyPal API
APP_VERSION=1.0.0
ENVIRONMENT=production
LOG_LEVEL=INFO
CORS_ORIGINS=*
REQUEST_TIMEOUT=30
MAX_REQUEST_SIZE=10000

# AI Model settings
AI_PROVIDER=deepseek
MODEL_TEMPERATURE=0.7
MODEL_MAX_TOKENS=500

# OpenAI settings (if using OpenAI)
OPENAI_API_KEY=your-openai-api-key
OPENAI_API_MODEL=gpt-3.5-turbo

# DeepSeek settings (if using DeepSeek)
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_API_BASE=https://api.deepseek.com/v1
DEEPSEEK_API_MODEL=deepseek-chat

# AWS settings
AWS_REGION=us-east-1

# DynamoDB settings
USE_MOCK_DYNAMODB=false
DYNAMODB_CUSTOMERS_TABLE=replypal-customers
DYNAMODB_SUBSCRIPTIONS_TABLE=replypal-subscriptions
DYNAMODB_USER_SUBSCRIPTIONS_TABLE=replypal-user-subscriptions
DYNAMODB_USAGE_RECORDS_TABLE=replypal-usage-records

# Stripe settings
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
STRIPE_BASIC_PRICE_ID=your-stripe-price-id

# Auto-renewal settings
AUTO_RENEWAL_TOKEN_THRESHOLD=1500
AUTO_RENEWAL_TIME_THRESHOLD_DAYS=30

# Frontend URLs
FRONTEND_SUCCESS_URL=https://your-domain.com/success
FRONTEND_CANCEL_URL=https://your-domain.com/cancel

# Google OAuth settings
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://your-api-domain.com/auth/google-callback
```

#### Timeout and Memory:

- Timeout: 30 seconds (or higher for AI model calls)
- Memory: 512 MB (minimum recommended)

### 4. IAM Role Permissions

Ensure your Lambda execution role has the following permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "dynamodb:GetItem",
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:DeleteItem",
        "dynamodb:Query",
        "dynamodb:Scan"
      ],
      "Resource": ["arn:aws:dynamodb:*:*:table/replypal-*"]
    }
  ]
}
```

### 5. API Gateway Integration

#### Option A: Using AWS Console

1. **Create API Gateway**:

   - Go to AWS API Gateway Console
   - Click "Create API" → "REST API" → "Build"
   - API name: `replypal-api`
   - Endpoint Type: `Regional`
   - Click "Create API"

2. **Set up Proxy Integration**:

   - Click "Actions" → "Create Resource"
   - Check "Configure as proxy resource"
   - Resource Name: `{proxy+}`
   - Resource Path: `/{proxy+}`
   - Enable CORS: Check this box
   - Click "Create Resource"

3. **Configure Lambda Integration**:

   - Select the `/{proxy+}` resource
   - Click "Actions" → "Create Method" → "ANY"
   - Integration type: "Lambda Function"
   - Check "Use Lambda Proxy integration"
   - Lambda Function: `replypal-api`
   - Click "Save" → "OK"

4. **Deploy API**:
   - Click "Actions" → "Deploy API"
   - Deployment stage: "New Stage"
   - Stage name: `prod`
   - Click "Deploy"

#### Option B: Using AWS CLI

```bash
# Create API Gateway
API_ID=$(aws apigateway create-rest-api \
  --name replypal-api \
  --description "ReplyPal API Gateway" \
  --endpoint-configuration types=REGIONAL \
  --query 'id' --output text)

echo "Created API Gateway with ID: $API_ID"

# Get root resource ID
ROOT_RESOURCE_ID=$(aws apigateway get-resources \
  --rest-api-id $API_ID \
  --query 'items[0].id' --output text)

# Create proxy resource
PROXY_RESOURCE_ID=$(aws apigateway create-resource \
  --rest-api-id $API_ID \
  --parent-id $ROOT_RESOURCE_ID \
  --path-part '{proxy+}' \
  --query 'id' --output text)

# Create ANY method for proxy resource
aws apigateway put-method \
  --rest-api-id $API_ID \
  --resource-id $PROXY_RESOURCE_ID \
  --http-method ANY \
  --authorization-type NONE

# Get Lambda function ARN
LAMBDA_ARN=$(aws lambda get-function \
  --function-name replypal-api \
  --query 'Configuration.FunctionArn' --output text)

# Set up Lambda integration
aws apigateway put-integration \
  --rest-api-id $API_ID \
  --resource-id $PROXY_RESOURCE_ID \
  --http-method ANY \
  --type AWS_PROXY \
  --integration-http-method POST \
  --uri "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/$LAMBDA_ARN/invocations"

# Add Lambda permission for API Gateway
aws lambda add-permission \
  --function-name replypal-api \
  --statement-id apigateway-invoke \
  --action lambda:InvokeFunction \
  --principal apigateway.amazonaws.com \
  --source-arn "arn:aws:execute-api:us-east-1:*:$API_ID/*/*"

# Deploy API
aws apigateway create-deployment \
  --rest-api-id $API_ID \
  --stage-name prod

# Get API URL
API_URL="https://$API_ID.execute-api.us-east-1.amazonaws.com/prod"
echo "API Gateway URL: $API_URL"
```

#### Option C: Using Setup Scripts

For convenience, use the provided setup scripts:

**Linux/Mac:**

```bash
chmod +x setup_api_gateway.sh
./setup_api_gateway.sh
```

**Windows PowerShell:**

```powershell
.\setup_api_gateway.ps1
```

### 6. Update Chrome Extension Settings

After setting up API Gateway, update your Chrome extension to use the new API URL:

1. **Get your API URL** from the setup output (format: `https://[api-id].execute-api.[region].amazonaws.com/prod`)

2. **Update extension settings**:

   - Open your Chrome extension
   - Go to Settings tab
   - Update the API URL to your new API Gateway URL
   - Save settings

3. **Test the connection** by trying to generate a response in the extension

### 7. Testing

Test the deployment:

```bash
# Replace YOUR_API_URL with your actual API Gateway URL
API_URL="https://your-api-id.execute-api.us-east-1.amazonaws.com/prod"

# Test health endpoint
curl $API_URL/ping

# Test with authentication
curl -X POST $API_URL/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "selected_text": "Hello, how are you?",
    "user_intent": "Reply politely",
    "tone": "friendly",
    "purpose": "reply"
  }'
```

## Important Notes

1. **Cold Starts**: Lambda functions may have cold start delays. Consider using provisioned concurrency for production.

2. **Package Size**: The deployment package is ~18MB, which is well within Lambda limits.

3. **Dependencies**: All required dependencies are included in the package.

4. **Environment Variables**: Make sure to set all required environment variables, especially API keys.

5. **DynamoDB**: Ensure your DynamoDB tables exist and the Lambda has proper permissions.

6. **Monitoring**: Use CloudWatch logs to monitor function execution and debug issues.

## Troubleshooting

### Common Issues:

1. **Import Errors**: Ensure all dependencies are included in the package
2. **Permission Errors**: Check IAM role permissions for DynamoDB access
3. **Timeout Errors**: Increase Lambda timeout for AI model calls
4. **Memory Errors**: Increase Lambda memory allocation

### Logs:

Check CloudWatch logs for detailed error messages:

```bash
aws logs describe-log-groups --log-group-name-prefix /aws/lambda/replypal-api
```

## Updating the Function

To update the function code:

1. Rebuild the package: `python build_lambda_package.py`
2. Upload the new ZIP file using AWS Console or CLI
3. Test the updated function

## Security Considerations

1. Store sensitive data (API keys) in AWS Secrets Manager
2. Use environment variables for configuration
3. Enable AWS X-Ray for tracing (optional)
4. Set up proper VPC configuration if needed
