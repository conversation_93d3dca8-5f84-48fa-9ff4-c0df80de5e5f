@echo off
REM Zappa deployment script for Windows
REM Usage: deploy.bat [action] [stage]
REM Example: deploy.bat update production

setlocal

REM Default values
set ACTION=%1
set STAGE=%2

if "%ACTION%"=="" set ACTION=status
if "%STAGE%"=="" set STAGE=production

echo.
echo ========================================
echo ReplyPal Zappa Deployment Tool
echo ========================================
echo Action: %ACTION%
echo Stage: %STAGE%
echo.

REM Check if we're in the right directory
if not exist "zappa_settings.json" (
    echo Error: zappa_settings.json not found
    echo Make sure you're in the fastapi directory
    exit /b 1
)

REM Activate virtual environment if it exists
if exist "env\Scripts\activate.bat" (
    echo Activating virtual environment...
    call env\Scripts\activate.bat
)

REM Run the Python deployment script
python deploy.py %ACTION% --stage %STAGE%

echo.
echo Done!
pause
