"""
Zappa-optimized entry point for ReplyPal FastAPI application.
This module provides the app instance specifically configured for Zappa deployment.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import the main FastAPI app
from app.main import app

# Configure for Zappa deployment
app.title = "ReplyPal API"
app.description = "AI-powered response generation API for Chrome extension"
app.version = os.getenv("APP_VERSION", "1.0.0")

# Zappa will use this app instance
# The app is already configured with all middleware, routes, and settings in app.main
