#!/usr/bin/env python3
"""
Setup script for Zappa deployment
Installs dependencies and prepares the environment for Zappa deployment
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, check=True):
    """Run a shell command."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def main():
    print("🚀 Setting up Zappa for ReplyPal deployment...")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("zappa_settings.json").exists():
        print("❌ zappa_settings.json not found. Make sure you're in the fastapi directory.")
        sys.exit(1)
    
    # Install dependencies
    print("📦 Installing dependencies...")
    if not run_command("pip install -r requirements.txt"):
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Check AWS CLI
    print("🔍 Checking AWS CLI...")
    if not run_command("aws --version", check=False):
        print("❌ AWS CLI not found. Please install AWS CLI first.")
        print("   Download from: https://aws.amazon.com/cli/")
        sys.exit(1)
    
    # Check AWS profile
    print("🔍 Checking AWS profile...")
    if not run_command("aws sts get-caller-identity --profile sumathy_aws", check=False):
        print("⚠️  AWS profile 'sumathy_aws' not configured.")
        print("   Please run: aws configure --profile sumathy_aws")
        print("   You can continue setup and configure AWS later.")
    else:
        print("✅ AWS profile configured")
    
    # Initialize Zappa (this creates the basic structure)
    print("🔧 Initializing Zappa...")
    if Path("zappa_settings.json").exists():
        print("✅ Zappa settings already exist")
    else:
        print("❌ Zappa settings not found")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ Setup complete!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Configure AWS if not done: aws configure --profile sumathy_aws")
    print("2. Deploy to staging: python deploy.py deploy --stage staging")
    print("3. Test the deployment: python deploy.py status --stage staging")
    print("4. Deploy to production: python deploy.py deploy --stage production")
    print("\nFor help: python deploy.py --help")
    print("=" * 50)

if __name__ == "__main__":
    main()
